const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Create-B-l6af6-.js","assets/vendor-DwpQ5WHX.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-lW_nm-2e.js","assets/createLucideIcon-Di250PjL.js","assets/AppLayout-B4Zax8Ug.css","assets/Index-BERqv8eI.js","assets/AppointmentDetail-2v4RAYCS.js","assets/AppointmentEdit-BLcMh4uO.js","assets/AppointmentPayment-CZKiHIWD.js","assets/Appointments-m31Pv5kU.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-BfGwU5tx.js","assets/ChatInput-B1to_S1K.js","assets/ChatInput-BmGnubPc.css","assets/Chat-Dyi9gz85.css","assets/ChatHistory-CLyOBQgw.js","assets/Chats-Du6qXBCX.js","assets/Clinics-CrvEl9-b.js","assets/Clubs-q9VJXA6x.js","assets/CreditHistory-Bi-u7rS9.js","assets/Credits-DYa6da4e.js","assets/Dashboard-CouluDPN.js","assets/Dashboard_backup-BXU-ngoy.js","assets/Dashboard_backup-CW3kC41H.css","assets/Discover-D_UI-4aL.js","assets/EmailTemplates-Dl90g-Bx.js","assets/Notifications-DPhm1yd1.js","assets/Patients-DLk4TZXQ.js","assets/Payments-ZZkm5i7n.js","assets/Permissions-Cbu1I3rg.js","assets/Availability-Q36GkBvG.js","assets/Earnings-B9Lz97_J.js","assets/Patients-B1Fb08UK.js","assets/Profile-CT8j8_sG.js","assets/Schedule-C1MBh6rO.js","assets/Services-C7wTBPDq.js","assets/ProviderRegister-Doxrtr7D.js","assets/InputError.vue_vue_type_script_setup_true_lang-DSYw3Bxn.js","assets/Providers-BkH3wxAj.js","assets/Referrals-Bp0s9yEr.js","assets/Services-CW_0C1pI.js","assets/Shop-CffslAxO.js","assets/Cart-CyCjfl3N.js","assets/ProductDetail-DURwI6B6.js","assets/SystemVerification-Bwlb6VEf.js","assets/Users-HjU7fTow.js","assets/Waitlist-DadhbisQ.js","assets/Welcome-1BB3Ju3k.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-DHmVY5mM.js","assets/index-CDjkLHA6.js","assets/Label.vue_vue_type_script_setup_true_lang-L1W7djSO.js","assets/index-0ZTvHY_i.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-CDXnnkHu.js","assets/ForgotPassword-BGrFuC79.js","assets/TextLink.vue_vue_type_script_setup_true_lang-CDOKKNGQ.js","assets/FounderSignup-Ca4DjReA.js","assets/Register-CKUozbt1.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-BpkFVzeQ.js","assets/VerifyEmail-CNlP8CYW.js","assets/Appearance-Dj9cSuuO.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-D0cv1GJg.js","assets/Layout.vue_vue_type_script_setup_true_lang-BFFH9o3p.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-DyIfMmI_.js","assets/useBodyScrollLock-Tkmq4KwB.js","assets/Password-CTjyXCpC.js","assets/Profile-BvRu2GYU.js"])))=>i.map(i=>d[i]);
import{r as A,o as S,c as g,w as I,a as v,L as O,W as w,b as D,k as V,h as k}from"./vendor-DwpQ5WHX.js";const C="modulepreload",x=function(e){return"/build/"+e},R={},t=function(r,o,u){let l=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),c=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));l=Promise.allSettled(o.map(i=>{if(i=x(i),i in R)return;R[i]=!0;const d=i.endsWith(".css"),n=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${n}`))return;const a=document.createElement("link");if(a.rel=d?"stylesheet":C,d||(a.as="script"),a.crossOrigin="",a.href=i,c&&a.setAttribute("nonce",c),document.head.appendChild(a),d)return new Promise((f,L)=>{a.addEventListener("load",f),a.addEventListener("error",()=>L(new Error(`Unable to preload CSS for ${i}`)))})}))}function _(s){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=s,window.dispatchEvent(c),!c.defaultPrevented)throw s}return l.then(s=>{for(const c of s||[])c.status==="rejected"&&_(c.reason);return r().catch(_)})};async function F(e,r){for(const o of Array.isArray(e)?e:[e]){const u=r[o];if(!(typeof u>"u"))return typeof u=="function"?u():u}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",o==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,o=365)=>{if(typeof document>"u")return;const u=o*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${u};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),y=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=y();P(e||"system")};function j(){var r;if(typeof window>"u")return;const e=y();P(e||"system"),(r=z())==null||r.addEventListener("change",$)}function Q(){const e=A("system");S(()=>{const o=localStorage.getItem("appearance");o&&(e.value=o)});function r(o){e.value=o,localStorage.setItem("appearance",o),b("appearance",o),P(o)}return{appearance:e,updateAppearance:r}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},p=A("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(p.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=g(()=>{var n;return((n=m[p.value])==null?void 0:n.scale)||1}),r=g(()=>{var n;return((n=m[p.value])==null?void 0:n.name)||"Normal"}),o=g(()=>{var n;return((n=m[p.value])==null?void 0:n.description)||""}),u=g(()=>Object.entries(m).map(([n,a])=>({key:n,...a}))),l=g(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),_=n=>{m[n]&&(p.value=n,q(n),d())},s=()=>{const n=Object.keys(m),a=n.indexOf(p.value);a<n.length-1&&_(n[a+1])},c=()=>{const n=Object.keys(m),a=n.indexOf(p.value);a>0&&_(n[a-1])},i=()=>{_("normal")},d=()=>{const n=document.documentElement;Object.entries(l.value).forEach(([a,f])=>{n.style.setProperty(a,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${p.value}`)};return I(p,()=>{d()},{immediate:!0}),{currentFontSize:p,fontSizeScale:e,fontSizeName:r,fontSizeDescription:o,availableFontSizes:u,fontSizeStyles:l,setFontSize:_,increaseFontSize:s,decreaseFontSize:c,resetFontSize:i,applyFontSizeToDocument:d,FONT_SIZES:m}}const X="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const E=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},h=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok)return await new Promise(r=>setTimeout(r,100)),E()}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},T=E();T&&(v.defaults.headers.common["X-CSRF-TOKEN"]=T);const U=async()=>{let e=E();return e||(console.log("Initializing CSRF token..."),e=await h(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let r=E();return r||(console.warn("No CSRF token found, attempting to refresh..."),r=await h()),r?e.headers["X-CSRF-TOKEN"]=r:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",e.headers["Content-Type"]=e.headers["Content-Type"]||"application/json",e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var o,u,l,_,s,c;const r=e.config;if(((o=e.response)==null?void 0:o.status)===419&&!r._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),r._retry=!0;try{const i=await h();if(i)return r.headers["X-CSRF-TOKEN"]=i,v.defaults.headers.common["X-CSRF-TOKEN"]=i,await new Promise(d=>setTimeout(d,50)),v(r);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(i){console.error("Error during token refresh:",i)}}if(((u=e.response)==null?void 0:u.status)===500&&!r._serverRetry&&!((l=r.url)!=null&&l.includes("/logout"))){console.warn("Server error detected, attempting retry..."),r._serverRetry=!0,await new Promise(i=>setTimeout(i,1e3));try{return v(r)}catch(i){console.error("Server error retry failed:",i)}}return(_=r.url)!=null&&_.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((s=e.response)==null?void 0:s.status)===401&&((c=r.url)!=null&&c.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;O({title:e=>`${e} - ${X}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-B-l6af6-.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index-BERqv8eI.js"),__vite__mapDeps([7,1,2,3,4,5,6])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-2v4RAYCS.js"),__vite__mapDeps([8,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-BLcMh4uO.js"),__vite__mapDeps([9,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-CZKiHIWD.js"),__vite__mapDeps([10,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-m31Pv5kU.js"),__vite__mapDeps([11,2,1,3,4,5,6,12])),"./pages/Chat.vue":()=>t(()=>import("./Chat-BfGwU5tx.js"),__vite__mapDeps([13,1,2,3,4,5,6,14,15,16])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-CLyOBQgw.js"),__vite__mapDeps([17,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-Du6qXBCX.js"),__vite__mapDeps([18,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-CrvEl9-b.js"),__vite__mapDeps([19,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-q9VJXA6x.js"),__vite__mapDeps([20,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-Bi-u7rS9.js"),__vite__mapDeps([21,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits-DYa6da4e.js"),__vite__mapDeps([22,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-CouluDPN.js"),__vite__mapDeps([23,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-BXU-ngoy.js"),__vite__mapDeps([24,1,2,3,4,5,6,25])),"./pages/Discover.vue":()=>t(()=>import("./Discover-D_UI-4aL.js"),__vite__mapDeps([26,2,1,3,4,5,6])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-Dl90g-Bx.js"),__vite__mapDeps([27,1,2,3,4,5,6])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-DPhm1yd1.js"),__vite__mapDeps([28,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-DLk4TZXQ.js"),__vite__mapDeps([29,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-ZZkm5i7n.js"),__vite__mapDeps([30,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-Cbu1I3rg.js"),__vite__mapDeps([31,2,1,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-Q36GkBvG.js"),__vite__mapDeps([32,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-B9Lz97_J.js"),__vite__mapDeps([33,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-B1Fb08UK.js"),__vite__mapDeps([34,1,2,3,4,5,6])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-CT8j8_sG.js"),__vite__mapDeps([35,1,2,3,4,5,6])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-C1MBh6rO.js"),__vite__mapDeps([36,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-C7wTBPDq.js"),__vite__mapDeps([37,2,1,3,4,5,6])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-Doxrtr7D.js"),__vite__mapDeps([38,1,39])),"./pages/Providers.vue":()=>t(()=>import("./Providers-BkH3wxAj.js"),__vite__mapDeps([40,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-Bp0s9yEr.js"),__vite__mapDeps([41,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-CW_0C1pI.js"),__vite__mapDeps([42,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-CffslAxO.js"),__vite__mapDeps([43,1,2,3,4,5,6])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-CyCjfl3N.js"),__vite__mapDeps([44,2,1,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-DURwI6B6.js"),__vite__mapDeps([45,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-Bwlb6VEf.js"),__vite__mapDeps([46,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-HjU7fTow.js"),__vite__mapDeps([47,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-DadhbisQ.js"),__vite__mapDeps([48,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-1BB3Ju3k.js"),__vite__mapDeps([49,1,14,3,15,50])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-DHmVY5mM.js"),__vite__mapDeps([51,1,39,52,4,53,54,55,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-BGrFuC79.js"),__vite__mapDeps([56,1,39,57,52,4,53,54,55,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-Ca4DjReA.js"),__vite__mapDeps([58,1,39])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-CKUozbt1.js"),__vite__mapDeps([59,1,39,3,60])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-BpkFVzeQ.js"),__vite__mapDeps([61,1,39,52,4,53,54,55,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-CNlP8CYW.js"),__vite__mapDeps([62,1,57,52,4,55,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-Dj9cSuuO.js"),__vite__mapDeps([63,1,3,5,64,2,4,6,65,52,54,66])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-DyIfMmI_.js"),__vite__mapDeps([67,1,65,52,4,54,53,68])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-CTjyXCpC.js"),__vite__mapDeps([69,1,39,2,3,4,5,6,65,52,54,64,53])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-BvRu2GYU.js"),__vite__mapDeps([70,1,64,39,52,4,68,54,53,5,2,3,6,65]))})),setup({el:e,App:r,props:o,plugin:u}){D({render:()=>k(r,o)}).use(u).use(V).mount(e)},progress:{color:"#4B5563"}}).then(()=>{w.on("error",e=>{var r,o,u,l,_,s;if(console.log("Inertia request error:",e),(o=(r=e.response)==null?void 0:r.url)!=null&&o.includes("/logout")||(_=(l=(u=e.response)==null?void 0:u.config)==null?void 0:l.url)!=null&&_.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((s=e.response)==null?void 0:s.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:K}=W();K();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});U().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{Q as a,W as u};
