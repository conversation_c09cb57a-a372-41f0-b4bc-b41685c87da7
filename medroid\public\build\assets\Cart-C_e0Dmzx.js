import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import{r as g,c as v,o as T,d,e as i,f as u,u as h,m as V,g as m,i as t,t as l,j as y,P as w,x,F as k,q as E,a as p,W as I}from"./vendor-DkZiYBIF.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const L={class:"py-12"},N={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},P={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},A={class:"p-6 bg-white border-b border-gray-200"},D={class:"flex items-center justify-between"},H={class:"text-gray-600 mt-1"},R={class:"flex items-center space-x-4"},W={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Y={key:1,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},z={class:"p-12 text-center"},O={key:2,class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},G={class:"lg:col-span-2"},J={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},U={class:"space-y-4"},X={class:"flex-shrink-0"},Z=["src","alt"],Q={class:"flex-1 min-w-0"},tt={class:"text-lg font-semibold text-gray-900"},et={class:"text-gray-600 text-sm"},st={class:"flex items-center mt-2"},ot={class:"text-lg font-bold text-blue-600"},rt={key:0,class:"ml-2 bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded"},at={class:"flex items-center space-x-2"},lt=["onClick","disabled"],dt={class:"w-12 text-center font-medium"},it=["onClick","disabled"],nt={class:"text-right"},ct={class:"text-lg font-bold text-gray-900"},ut=["onClick","disabled"],pt={class:"lg:col-span-1"},gt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6"},vt={class:"p-6"},ht={class:"space-y-3 mb-6"},mt={class:"flex justify-between"},xt={class:"font-medium"},ft={class:"border-t pt-3"},_t={class:"flex justify-between"},bt={class:"text-lg font-bold text-blue-600"},Ft={__name:"Cart",setup(yt){const C=[{title:"Shop",href:"/shop"},{title:"Shopping Cart",href:"/shop/cart"}],r=g([]),c=g(!1),n=g({}),j=v(()=>r.value.reduce((o,e)=>o+e.total_price,0)),f=v(()=>"$"+j.value.toFixed(2)),_=v(()=>r.value.reduce((o,e)=>o+e.quantity,0)),S=async()=>{c.value=!0;try{const o=await p.get("/shop/cart");r.value=o.data.cart_items||[]}catch(o){console.error("Error loading cart:",o),r.value=[]}finally{c.value=!1}},b=async(o,e)=>{if(!(e<0)){n.value[o.product_id]=!0;try{const s=await p.put(`/shop/cart/${o.product_id}`,{quantity:e});if(s.data.success)if(e===0)r.value=r.value.filter(a=>a.product_id!==o.product_id);else{const a=r.value.findIndex(M=>M.product_id===o.product_id);a!==-1&&(r.value[a].quantity=e,r.value[a].total_price=e*r.value[a].price)}else alert(s.data.message||"Failed to update cart")}catch(s){console.error("Error updating cart:",s),alert("Failed to update cart")}finally{n.value[o.product_id]=!1}}},F=async o=>{if(confirm("Remove this item from cart?")){n.value[o.product_id]=!0;try{const e=await p.delete(`/shop/cart/${o.product_id}`);e.data.success?r.value=r.value.filter(s=>s.product_id!==o.product_id):alert(e.data.message||"Failed to remove item")}catch(e){console.error("Error removing item:",e),alert("Failed to remove item")}finally{n.value[o.product_id]=!1}}},$=async()=>{if(confirm("Clear all items from cart?")){c.value=!0;try{const o=await p.delete("/shop/cart");o.data.success?r.value=[]:alert(o.data.message||"Failed to clear cart")}catch(o){console.error("Error clearing cart:",o),alert("Failed to clear cart")}finally{c.value=!1}}},q=()=>{if(r.value.length===0){alert("Your cart is empty");return}I.visit("/shop/checkout")};return T(()=>{S()}),(o,e)=>(i(),d(k,null,[u(h(V),{title:"Shopping Cart - Medroid"}),u(B,{breadcrumbs:C},{default:m(()=>[t("div",L,[t("div",N,[t("div",P,[t("div",A,[t("div",D,[t("div",null,[e[0]||(e[0]=t("h1",{class:"text-2xl font-bold text-gray-900"},"Shopping Cart",-1)),t("p",H,l(_.value)+" "+l(_.value===1?"item":"items")+" in your cart",1)]),t("div",R,[u(h(w),{href:"/shop",class:"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},{default:m(()=>e[1]||(e[1]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),x(" Continue Shopping ")])),_:1}),r.value.length>0?(i(),d("button",{key:0,onClick:$,class:"inline-flex items-center px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"},e[2]||(e[2]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),x(" Clear Cart ")]))):y("",!0)])])])]),c.value?(i(),d("div",W,e[3]||(e[3]=[t("div",{class:"p-12 text-center"},[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t("p",{class:"mt-2 text-gray-600"},"Loading cart...")],-1)]))):r.value.length===0?(i(),d("div",Y,[t("div",z,[e[5]||(e[5]=t("div",{class:"text-6xl mb-4"},"🛒",-1)),e[6]||(e[6]=t("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Your cart is empty",-1)),e[7]||(e[7]=t("p",{class:"text-gray-600 mb-6"},"Add some products to get started!",-1)),u(h(w),{href:"/shop",class:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"},{default:m(()=>e[4]||(e[4]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})],-1),x(" Start Shopping ")])),_:1})])])):(i(),d("div",O,[t("div",G,[t("div",J,[t("div",K,[e[8]||(e[8]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Cart Items",-1)),t("div",U,[(i(!0),d(k,null,E(r.value,s=>(i(),d("div",{key:s.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[t("div",X,[t("img",{src:s.product.primary_image||"/images/placeholder-product.jpg",alt:s.product.name,class:"h-20 w-20 object-cover rounded-lg"},null,8,Z)]),t("div",Q,[t("h3",tt,l(s.product.name),1),t("p",et,l(s.product.short_description),1),t("div",st,[t("span",ot,"$"+l(s.price.toFixed(2)),1),s.product.type==="digital"?(i(),d("span",rt," Digital ")):y("",!0)])]),t("div",at,[t("button",{onClick:a=>b(s,s.quantity-1),disabled:n.value[s.product_id]||s.quantity<=1,class:"w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"}," - ",8,lt),t("span",dt,l(s.quantity),1),t("button",{onClick:a=>b(s,s.quantity+1),disabled:n.value[s.product_id],class:"w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"}," + ",8,it)]),t("div",nt,[t("div",ct,"$"+l(s.total_price.toFixed(2)),1),t("button",{onClick:a=>F(s),disabled:n.value[s.product_id],class:"text-red-600 hover:text-red-800 text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Remove ",8,ut)])]))),128))])])])]),t("div",pt,[t("div",gt,[t("div",vt,[e[13]||(e[13]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),t("div",ht,[t("div",mt,[e[9]||(e[9]=t("span",{class:"text-gray-600"},"Subtotal",-1)),t("span",xt,l(f.value),1)]),e[11]||(e[11]=t("div",{class:"flex justify-between"},[t("span",{class:"text-gray-600"},"Shipping"),t("span",{class:"font-medium"},"Calculated at checkout")],-1)),e[12]||(e[12]=t("div",{class:"flex justify-between"},[t("span",{class:"text-gray-600"},"Tax"),t("span",{class:"font-medium"},"Calculated at checkout")],-1)),t("div",ft,[t("div",_t,[e[10]||(e[10]=t("span",{class:"text-lg font-semibold"},"Total",-1)),t("span",bt,l(f.value),1)])])]),t("button",{onClick:q,class:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"}," Proceed to Checkout "),e[14]||(e[14]=t("div",{class:"mt-4 text-center"},[t("p",{class:"text-sm text-gray-500"}," Secure checkout powered by Stripe ")],-1))])])])]))])])]),_:1})],64))}};export{Ft as default};
