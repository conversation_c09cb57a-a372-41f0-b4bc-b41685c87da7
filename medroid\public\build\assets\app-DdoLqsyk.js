const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Index-BdtTRyDZ.js","assets/vendor-DkZiYBIF.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-fZxfkI-v.js","assets/createLucideIcon-CSZE2I1R.js","assets/AppLayout-B4Zax8Ug.css","assets/Show-CYsnbK6D.js","assets/Create-DD38BS-N.js","assets/Index-zGV6kEkJ.js","assets/AppointmentDetail-CufvaAUk.js","assets/AppointmentEdit-Ce4nYDgI.js","assets/AppointmentPayment-DpDdMRb4.js","assets/Appointments-C3VGeb7e.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-ynRQ6Mio.js","assets/ChatInput-aoaXzTX4.js","assets/ChatInput-BmGnubPc.css","assets/Chat-Dyi9gz85.css","assets/ChatHistory-lf9KUcwi.js","assets/Chats-B4b0KOv2.js","assets/Clinics-C-aM7FVt.js","assets/Clubs-DNXRj8_3.js","assets/CreditHistory-BlKYolnw.js","assets/Credits-ezgpa-kj.js","assets/Dashboard-CWZCiu5Y.js","assets/Dashboard_backup-DhpTTGhG.js","assets/Dashboard_backup-CW3kC41H.css","assets/Discover-DxKfPmrQ.js","assets/EmailTemplates-DydWptXS.js","assets/Notifications-BF1XvCqv.js","assets/Patients-6ons8DrM.js","assets/Payments-hczwd3nD.js","assets/Permissions-COG9NHCY.js","assets/Availability-8uYa1RfZ.js","assets/Earnings-DKtkO8Tr.js","assets/Patients-6MZZPDN2.js","assets/Profile-CNOrcXnD.js","assets/Schedule-VsvN1-TT.js","assets/Services-CNzcPm1k.js","assets/ProviderRegister-Ce9wNraF.js","assets/InputError.vue_vue_type_script_setup_true_lang-CSFrrJiY.js","assets/Providers-DfogisOT.js","assets/Referrals-CFncy49u.js","assets/Services-DDQisKXI.js","assets/Shop-o17z-G_E.js","assets/Cart-C_e0Dmzx.js","assets/ProductDetail-G1RoJ5bk.js","assets/SystemVerification-CgW2-LKY.js","assets/Users-cQCs2LHS.js","assets/Waitlist-DeTWI3Oh.js","assets/Welcome-CpGm4PDf.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-ByuK1FP5.js","assets/index-DNsgB9iJ.js","assets/Label.vue_vue_type_script_setup_true_lang-CbHOsuTJ.js","assets/index-CM-BHwaf.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-DMSNRIAp.js","assets/ForgotPassword-BuaztFcp.js","assets/TextLink.vue_vue_type_script_setup_true_lang-Cj4YiVUD.js","assets/FounderSignup-DE50RecL.js","assets/Register-qmh5cEtw.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-28xktSbz.js","assets/VerifyEmail-l9NOxKKe.js","assets/Appearance-DEhQdVcL.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-DIBhJu7Q.js","assets/Layout.vue_vue_type_script_setup_true_lang-BM08YvWd.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-DYJNSVGD.js","assets/useBodyScrollLock-CEKoJB3U.js","assets/Password-Cc8bmDn1.js","assets/Profile-Gwdt4Stv.js"])))=>i.map(i=>d[i]);
import{r as T,o as O,c as g,w as I,a as v,L as S,W as w,b as D,k as V,h as k}from"./vendor-DkZiYBIF.js";const C="modulepreload",x=function(e){return"/build/"+e},A={},t=function(r,o,u){let d=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),c=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));d=Promise.allSettled(o.map(i=>{if(i=x(i),i in A)return;A[i]=!0;const l=i.endsWith(".css"),n=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${n}`))return;const a=document.createElement("link");if(a.rel=l?"stylesheet":C,l||(a.as="script"),a.crossOrigin="",a.href=i,c&&a.setAttribute("nonce",c),document.head.appendChild(a),l)return new Promise((f,y)=>{a.addEventListener("load",f),a.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${i}`)))})}))}function _(s){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=s,window.dispatchEvent(c),!c.defaultPrevented)throw s}return d.then(s=>{for(const c of s||[])c.status==="rejected"&&_(c.reason);return r().catch(_)})};async function F(e,r){for(const o of Array.isArray(e)?e:[e]){const u=r[o];if(!(typeof u>"u"))return typeof u=="function"?u():u}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",o==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,o=365)=>{if(typeof document>"u")return;const u=o*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${u};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();P(e||"system")};function j(){var r;if(typeof window>"u")return;const e=L();P(e||"system"),(r=z())==null||r.addEventListener("change",$)}function Q(){const e=T("system");O(()=>{const o=localStorage.getItem("appearance");o&&(e.value=o)});function r(o){e.value=o,localStorage.setItem("appearance",o),b("appearance",o),P(o)}return{appearance:e,updateAppearance:r}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},p=T("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(p.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=g(()=>{var n;return((n=m[p.value])==null?void 0:n.scale)||1}),r=g(()=>{var n;return((n=m[p.value])==null?void 0:n.name)||"Normal"}),o=g(()=>{var n;return((n=m[p.value])==null?void 0:n.description)||""}),u=g(()=>Object.entries(m).map(([n,a])=>({key:n,...a}))),d=g(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),_=n=>{m[n]&&(p.value=n,q(n),l())},s=()=>{const n=Object.keys(m),a=n.indexOf(p.value);a<n.length-1&&_(n[a+1])},c=()=>{const n=Object.keys(m),a=n.indexOf(p.value);a>0&&_(n[a-1])},i=()=>{_("normal")},l=()=>{const n=document.documentElement;Object.entries(d.value).forEach(([a,f])=>{n.style.setProperty(a,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${p.value}`)};return I(p,()=>{l()},{immediate:!0}),{currentFontSize:p,fontSizeScale:e,fontSizeName:r,fontSizeDescription:o,availableFontSizes:u,fontSizeStyles:d,setFontSize:_,increaseFontSize:s,decreaseFontSize:c,resetFontSize:i,applyFontSizeToDocument:l,FONT_SIZES:m}}const X="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const E=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},h=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok)return await new Promise(r=>setTimeout(r,100)),E()}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},R=E();R&&(v.defaults.headers.common["X-CSRF-TOKEN"]=R);const U=async()=>{let e=E();return e||(console.log("Initializing CSRF token..."),e=await h(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let r=E();return r||(console.warn("No CSRF token found, attempting to refresh..."),r=await h()),r?e.headers["X-CSRF-TOKEN"]=r:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",e.headers["Content-Type"]=e.headers["Content-Type"]||"application/json",e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var o,u,d,_,s,c;const r=e.config;if(((o=e.response)==null?void 0:o.status)===419&&!r._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),r._retry=!0;try{const i=await h();if(i)return r.headers["X-CSRF-TOKEN"]=i,v.defaults.headers.common["X-CSRF-TOKEN"]=i,await new Promise(l=>setTimeout(l,50)),v(r);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(i){console.error("Error during token refresh:",i)}}if(((u=e.response)==null?void 0:u.status)===500&&!r._serverRetry&&!((d=r.url)!=null&&d.includes("/logout"))){console.warn("Server error detected, attempting retry..."),r._serverRetry=!0,await new Promise(i=>setTimeout(i,1e3));try{return v(r)}catch(i){console.error("Server error retry failed:",i)}}return(_=r.url)!=null&&_.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((s=e.response)==null?void 0:s.status)===401&&((c=r.url)!=null&&c.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;S({title:e=>`${e} - ${X}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Orders/Index.vue":()=>t(()=>import("./Index-BdtTRyDZ.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Orders/Show.vue":()=>t(()=>import("./Show-CYsnbK6D.js"),__vite__mapDeps([7,2,1,3,4,5,6])),"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-DD38BS-N.js"),__vite__mapDeps([8,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index-zGV6kEkJ.js"),__vite__mapDeps([9,1,2,3,4,5,6])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-CufvaAUk.js"),__vite__mapDeps([10,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-Ce4nYDgI.js"),__vite__mapDeps([11,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-DpDdMRb4.js"),__vite__mapDeps([12,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-C3VGeb7e.js"),__vite__mapDeps([13,2,1,3,4,5,6,14])),"./pages/Chat.vue":()=>t(()=>import("./Chat-ynRQ6Mio.js"),__vite__mapDeps([15,1,2,3,4,5,6,16,17,18])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-lf9KUcwi.js"),__vite__mapDeps([19,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-B4b0KOv2.js"),__vite__mapDeps([20,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-C-aM7FVt.js"),__vite__mapDeps([21,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-DNXRj8_3.js"),__vite__mapDeps([22,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-BlKYolnw.js"),__vite__mapDeps([23,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits-ezgpa-kj.js"),__vite__mapDeps([24,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-CWZCiu5Y.js"),__vite__mapDeps([25,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-DhpTTGhG.js"),__vite__mapDeps([26,1,2,3,4,5,6,27])),"./pages/Discover.vue":()=>t(()=>import("./Discover-DxKfPmrQ.js"),__vite__mapDeps([28,2,1,3,4,5,6])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-DydWptXS.js"),__vite__mapDeps([29,1,2,3,4,5,6])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-BF1XvCqv.js"),__vite__mapDeps([30,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-6ons8DrM.js"),__vite__mapDeps([31,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-hczwd3nD.js"),__vite__mapDeps([32,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-COG9NHCY.js"),__vite__mapDeps([33,2,1,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-8uYa1RfZ.js"),__vite__mapDeps([34,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-DKtkO8Tr.js"),__vite__mapDeps([35,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-6MZZPDN2.js"),__vite__mapDeps([36,1,2,3,4,5,6])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-CNOrcXnD.js"),__vite__mapDeps([37,1,2,3,4,5,6])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-VsvN1-TT.js"),__vite__mapDeps([38,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-CNzcPm1k.js"),__vite__mapDeps([39,2,1,3,4,5,6])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-Ce9wNraF.js"),__vite__mapDeps([40,1,41])),"./pages/Providers.vue":()=>t(()=>import("./Providers-DfogisOT.js"),__vite__mapDeps([42,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-CFncy49u.js"),__vite__mapDeps([43,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-DDQisKXI.js"),__vite__mapDeps([44,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-o17z-G_E.js"),__vite__mapDeps([45,1,2,3,4,5,6])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-C_e0Dmzx.js"),__vite__mapDeps([46,2,1,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-G1RoJ5bk.js"),__vite__mapDeps([47,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-CgW2-LKY.js"),__vite__mapDeps([48,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-cQCs2LHS.js"),__vite__mapDeps([49,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-DeTWI3Oh.js"),__vite__mapDeps([50,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-CpGm4PDf.js"),__vite__mapDeps([51,1,16,3,17,52])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-ByuK1FP5.js"),__vite__mapDeps([53,1,41,54,4,55,56,57,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-BuaztFcp.js"),__vite__mapDeps([58,1,41,59,54,4,55,56,57,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-DE50RecL.js"),__vite__mapDeps([60,1,41])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-qmh5cEtw.js"),__vite__mapDeps([61,1,41,3,62])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-28xktSbz.js"),__vite__mapDeps([63,1,41,54,4,55,56,57,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-l9NOxKKe.js"),__vite__mapDeps([64,1,59,54,4,57,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-DEhQdVcL.js"),__vite__mapDeps([65,1,3,5,66,2,4,6,67,54,56,68])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-DYJNSVGD.js"),__vite__mapDeps([69,1,67,54,4,56,55,70])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-Cc8bmDn1.js"),__vite__mapDeps([71,1,41,2,3,4,5,6,67,54,56,66,55])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-Gwdt4Stv.js"),__vite__mapDeps([72,1,66,41,54,4,70,56,55,5,2,3,6,67]))})),setup({el:e,App:r,props:o,plugin:u}){D({render:()=>k(r,o)}).use(u).use(V).mount(e)},progress:{color:"#4B5563"}}).then(()=>{w.on("error",e=>{var r,o,u,d,_,s;if(console.log("Inertia request error:",e),(o=(r=e.response)==null?void 0:r.url)!=null&&o.includes("/logout")||(_=(d=(u=e.response)==null?void 0:u.config)==null?void 0:d.url)!=null&&_.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((s=e.response)==null?void 0:s.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:K}=W();K();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});U().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{Q as a,W as u};
