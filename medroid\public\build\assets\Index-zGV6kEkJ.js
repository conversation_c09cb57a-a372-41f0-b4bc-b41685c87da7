import{n as K,c as x,r as g,o as Q,d as l,e as r,f as S,u as y,m as G,g as p,i as e,l as D,v as H,F as f,q as A,t as d,p as M,s as E,x as m,j as v,y as B,P as h}from"./vendor-DkZiYBIF.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const O={class:"flex items-center justify-between"},W={class:"flex mt-2","aria-label":"Breadcrumb"},X={class:"inline-flex items-center space-x-1 md:space-x-3"},Y={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Z={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},ee={class:"text-xs text-gray-500 mb-2"},te={class:"py-12"},se={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},ae={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},re={class:"p-6"},le={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},de=["value"],oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ne={class:"p-6 text-gray-900 dark:text-gray-100"},ie={key:0,class:"text-center py-8"},ue={key:1,class:"text-center py-8"},ce={key:2,class:"overflow-x-auto"},xe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ge={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},pe={class:"px-6 py-4 whitespace-nowrap"},ye={class:"flex items-center"},me={class:"ml-4"},ve={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},fe={class:"text-sm text-gray-500 dark:text-gray-400"},he={class:"px-6 py-4 whitespace-nowrap"},be={class:"text-sm text-gray-900 dark:text-gray-100"},ke={class:"px-6 py-4 whitespace-nowrap"},_e={class:"px-6 py-4 whitespace-nowrap"},we={class:"text-sm text-gray-900 dark:text-gray-100"},Pe={key:0,class:"text-xs text-gray-500 line-through ml-1"},Ce={class:"px-6 py-4 whitespace-nowrap"},Se={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},De={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Ue={__name:"Index",setup(Ae){const j=K(),o=x(()=>{var a;return(a=j.props.auth)==null?void 0:a.user}),b=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"}],k=g(!1),_=g([]),V=g([]),n=g(""),u=g("all"),c=g("all"),L=async()=>{var a;k.value=!0;try{const t=new URLSearchParams;n.value&&t.append("search",n.value),u.value!=="all"&&t.append("category",u.value),c.value!=="all"&&t.append("type",c.value);const s=await window.axios.get(`/admin/products-list?${t.toString()}`);_.value=((a=s.data.products)==null?void 0:a.data)||s.data.products||[],s.data.categories&&(V.value=s.data.categories)}catch(t){console.error("Error fetching products:",t),_.value=[]}finally{k.value=!1}},$=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",F=a=>a==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",N=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),T=x(()=>_.value.filter(a=>{const t=!n.value||a.name.toLowerCase().includes(n.value.toLowerCase())||a.sku.toLowerCase().includes(n.value.toLowerCase()),s=u.value==="all"||a.category_id==u.value,i=c.value==="all"||a.type===c.value;return t&&s&&i})),w=x(()=>{var a,t;return((t=(a=o.value)==null?void 0:a.roles)==null?void 0:t.some(s=>s.name==="admin"))||!1}),U=x(()=>{var a,t;return((t=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("create products"))||!1}),I=x(()=>{var a,t;return((t=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("edit products"))||!1}),z=x(()=>{var a,t;return((t=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("delete products"))||!1}),R=a=>{var t;return w.value?!0:I.value&&a.user_id===((t=o.value)==null?void 0:t.id)},q=a=>{var t;return w.value?!0:z.value&&a.user_id===((t=o.value)==null?void 0:t.id)};return Q(()=>{L()}),(a,t)=>(r(),l(f,null,[S(y(G),{title:"Product Management"}),S(J,null,{header:p(()=>{var s,i;return[e("div",O,[e("div",null,[t[4]||(t[4]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Product Management ",-1)),e("nav",W,[e("ol",X,[(r(),l(f,null,A(b,(P,C)=>e("li",{key:C,class:"inline-flex items-center"},[C<b.length-1?(r(),B(y(h),{key:0,href:P.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:p(()=>[m(d(P.title),1)]),_:2},1032,["href"])):(r(),l("span",Y,d(P.title),1)),C<b.length-1?(r(),l("svg",Z,t[3]||(t[3]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),e("div",ee," Debug: isAdmin="+d(w.value)+", canCreateProducts="+d(U.value)+", userPermissions="+d(((i=(s=o.value)==null?void 0:s.user_permissions)==null?void 0:i.join(", "))||"none"),1),U.value?(r(),B(y(h),{key:0,href:"/admin/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:p(()=>t[5]||(t[5]=[m(" Add Product ")])),_:1})):v("",!0)])]}),default:p(()=>[e("div",te,[e("div",se,[e("div",ae,[e("div",re,[e("div",le,[e("div",null,[D(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>n.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[H,n.value]])]),e("div",null,[D(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[t[6]||(t[6]=e("option",{value:"all"},"All Categories",-1)),(r(!0),l(f,null,A(V.value,s=>(r(),l("option",{key:s.id,value:s.id},d(s.name),9,de))),128))],512),[[M,u.value]])]),e("div",null,[D(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>c.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},t[7]||(t[7]=[e("option",{value:"all"},"All Types",-1),e("option",{value:"physical"},"Physical",-1),e("option",{value:"digital"},"Digital",-1)]),512),[[M,c.value]])]),e("div",null,[e("button",{onClick:L,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),e("div",oe,[e("div",ne,[k.value?(r(),l("div",ie,t[8]||(t[8]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):T.value.length===0?(r(),l("div",ue,t[9]||(t[9]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(r(),l("div",ce,[e("table",xe,[t[13]||(t[13]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",ge,[(r(!0),l(f,null,A(T.value,s=>{var i;return r(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",pe,[e("div",ye,[t[10]||(t[10]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[e("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"})])],-1)),e("div",me,[e("div",ve,d(s.name),1),e("div",fe," SKU: "+d(s.sku),1)])])]),e("td",he,[e("span",be,d(((i=s.category)==null?void 0:i.name)||"N/A"),1)]),e("td",ke,[e("span",{class:E([F(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.type),3)]),e("td",_e,[e("div",we,[m(d(N(s.price))+" ",1),s.sale_price?(r(),l("span",Pe,d(N(s.sale_price)),1)):v("",!0)])]),e("td",Ce,[e("span",{class:E([$(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.is_active?"Active":"Inactive"),3)]),e("td",Se,[S(y(h),{href:`/admin/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:p(()=>t[11]||(t[11]=[m(" View ")])),_:2},1032,["href"]),R(s)?(r(),B(y(h),{key:0,href:`/admin/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:p(()=>t[12]||(t[12]=[m(" Edit ")])),_:2},1032,["href"])):v("",!0),q(s)?(r(),l("button",De," Delete ")):v("",!0)])])}),128))])])]))])])])])]),_:1})],64))}};export{Ue as default};
